import { Job } from "agenda";
import { Types } from "mongoose";
import runJob from "~/agenda/run";
import { MACHINE_DOCUMENTATION_STATUSES } from "~/constants/machine";
import { IMachine } from "~/datamodels/Machine/interface";
import serverEnvironmentTag from "~/environment/_server-tag";
import { IContext } from "~/types/common";
import logger from "~/utils/logger";

export const enableBoxWebhookForAsset = async (
  dataSources: IContext["dataSources"],
  { attrs: { data } }: Job<{ machineID: string }>,
) => {
  if (!data?.machineID)
    throw new Error(`"machineID" not present in data "${JSON.stringify(data)}"`);

  const machine: IMachine = await dataSources.Machine.loadOne(new Types.ObjectId(data.machineID));
  const boxOemUserName = `${machine.oem!.toString()}${serverEnvironmentTag}`;

  if (
    machine.template &&
    (machine.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.DETACHING ||
      machine.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHING)
  ) {
    return null;
  }

  const isTemplateAttached =
    machine.template &&
    machine.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHED;
  const machineIds = {
    externalFolder: machine.documentFolders?.externalId,
    internalFolder: machine.documentFolders?.internalId,
  };
  const webhookIds = {
    externalFolder: machine.documentFolders?.externalFolderBoxWebhookID,
    internalFolder: machine.documentFolders?.internalFolderBoxWebhookID,
  };

  if (isTemplateAttached) {
    const template = await dataSources.MachineTemplate.loadOne(machine.template);
    machineIds.externalFolder = template?.documentFolders?.externalId;
    machineIds.internalFolder = template?.documentFolders?.internalId;
  }

  if (webhookIds.externalFolder && webhookIds.internalFolder)
    return logger.info(`Webhooks for asset "${data.machineID}" already registered`);

  const [externalFolderBoxWebhookID, internalFolderBoxWebhookID] = await Promise.all([
    dataSources.BoxApi.createWebhookForFolder({
      boxOemUserName,
      folderID: machineIds.externalFolder!,
    }),
    dataSources.BoxApi.createWebhookForFolder({
      boxOemUserName,
      folderID: machineIds.internalFolder!,
    }),
  ]);
  logger.info(
    `Created webhooks "${externalFolderBoxWebhookID}" and "${internalFolderBoxWebhookID}" for asset "${data.machineID}"`,
  );

  const maybeUpdatedMachine = await dataSources.Machine.Machine.findOneAndUpdate(
    {
      _id: new Types.ObjectId(data.machineID),
      $and: [
        {
          $or: [
            { "documentFolders.externalFolderBoxWebhookID": { $exists: false } },
            { "documentFolders.externalFolderBoxWebhookID": { $eq: null } },
          ],
        },
        {
          $or: [
            { "documentFolders.internalFolderBoxWebhookID": { $exists: false } },
            { "documentFolders.internalFolderBoxWebhookID": { $eq: null } },
          ],
        },
      ],
    },
    {
      $set: {
        "documentFolders.externalFolderBoxWebhookID": externalFolderBoxWebhookID,
        "documentFolders.internalFolderBoxWebhookID": internalFolderBoxWebhookID,
      },
    },
    { new: true },
  );

  if (!maybeUpdatedMachine)
    await Promise.all([
      dataSources.BoxApi.deleteWebhookForFolder({
        boxOemUserName,
        webhookID: externalFolderBoxWebhookID,
      }),
      dataSources.BoxApi.deleteWebhookForFolder({
        boxOemUserName,
        webhookID: internalFolderBoxWebhookID,
      }),
    ]);
  else logger.info(`Webhooks for asset "${data.machineID}" registered successfully`);
};

export const deleteBoxWebhookForAsset = async (
  dataSources: IContext["dataSources"],
  { attrs: { data } }: Job<{ machineID: string; jobToRunAfterCompletion?: string }>,
) => {
  if (!data?.machineID)
    throw new Error(`"machineID" not present in data "${JSON.stringify(data)}"`);

  const machine: IMachine = await dataSources.Machine.loadOne(new Types.ObjectId(data.machineID));
  const boxOemUserName = `${machine.oem!.toString()}${serverEnvironmentTag}`;

  const externalFolderBoxWebhookID = machine.documentFolders?.externalFolderBoxWebhookID;
  const internalFolderBoxWebhookID = machine.documentFolders?.internalFolderBoxWebhookID;

  if (!(externalFolderBoxWebhookID || internalFolderBoxWebhookID))
    return logger.info(`Webhooks for asset "${data.machineID}" not found`);

  await Promise.all([
    dataSources.BoxApi.deleteWebhookForFolder({
      boxOemUserName,
      webhookID: externalFolderBoxWebhookID!,
    }),
    dataSources.BoxApi.deleteWebhookForFolder({
      boxOemUserName,
      webhookID: internalFolderBoxWebhookID!,
    }),
  ]);

  await dataSources.Machine.Machine.findOneAndUpdate(
    {
      _id: new Types.ObjectId(data.machineID),
    },
    {
      $set: {
        "documentFolders.externalFolderBoxWebhookID": null,
        "documentFolders.internalFolderBoxWebhookID": null,
      },
    },
  );

  if (data.jobToRunAfterCompletion)
    runJob({
      machineID: data.machineID,
      jobName: data.jobToRunAfterCompletion,
    });

  logger.info(`Webhooks for asset "${data.machineID}" deleted successfully`);
};
