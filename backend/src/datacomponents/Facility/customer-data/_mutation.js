import to from "await-to-js";
import { roles } from "~/directives";
import { throwIfError, belongsToUserTeams, isSelfCreated } from "~/utils";
import { deleteJobsByAssetId } from "~/utils/assets";
import { ERROR, FORBIDDEN } from "~/environment";

import { createCustomer } from "~/services/customer/create";
import { updateCustomer } from "~/services/customer/update";

export const mutationTypes = `#graphql
  type Mutation {
    createOemCustomer(input: InputCreateOemCustomer!): Customer @${roles.is.staff}
    createOemCustomerV2(input: InputCreateOemCustomerV2!): Customer @${roles.is.staff}
    updateOemCustomer(input: InputUpdateOemCustomer!): Customer @${roles.is.staff}
    deleteOemCustomer(customerId: ID!): String @${roles.is.staff}
    assignMultipleCustomersToTeam(input: InputAssignMultipleCustomersToTeam!): [Customer] @${roles.is.staff}
    unassignCustomerFromTeam(input: InputUnassignCustomerFromTeam): Customer @${roles.is.staff}
  }
`;

export const mutationResolvers = {
  Mutation: {
    createOemCustomer: async (_, args, { dataSources, user }) => {
      const { input } = args || {};
      if (!input) return null;
      const { name } = input;
      if (!name) return null;
      const [err, newCustomer] = await to(
        dataSources.Customer.save({
          name,
          oem: user?.organization,
          createdBy: user.id,
        }),
      );
      throwIfError(err, ERROR.USER.BAD_USER_INPUT);
      return newCustomer;
    },
    createOemCustomerV2: async (_, args, { dataSources, user, req: { t: translate } }) =>
      createCustomer({ args, dataSources, user, translate }),
    updateOemCustomer: async (_, args, { dataSources, user, req: { t: translate } }) =>
      updateCustomer({ args, dataSources, user, translate }),
    deleteOemCustomer: async (_, args, { dataSources, user }) => {
      const { customerId } = args || {};
      if (!customerId) return null;

      const oemCustomer = await dataSources.Customer.getOne({
        _id: customerId,
        oem: user.organization,
        ...belongsToUserTeams(user),
      });

      if (!oemCustomer) return null;
      if (!isSelfCreated(user, oemCustomer)) throw Error(FORBIDDEN);

      await dataSources.Customer.updateOne({ _id: customerId }, { $unset: { linkedOrg: "" } });

      if (oemCustomer?.oem?.toString() === user?.organization?.toString()) {
        const [allCustomerMachines] = await Promise.all([
          dataSources.Machine.loadManyByQuery({
            customer: customerId,
            oem: user.organization,
          }),
          dataSources.Customer.softDeleteById(customerId, user.id),
          dataSources.Contact.softDeleteMany({ connection: customerId }, user.id),
          dataSources.CustomerPortal.softDeleteMany({ connection: customerId }, user.id),
        ]);

        const deleteJobsQueries = [];
        const deletePreventiveMaintQueries = [];

        allCustomerMachines.forEach(async machine => {
          deleteJobsQueries.push(deleteJobsByAssetId({ dataSources, assetId: machine._id }));
          deletePreventiveMaintQueries.push(
            dataSources.PreventiveMaintenance.softDeleteMany(
              {
                machine: machine._id,
              },
              user.id,
            ),
          );
        });

        await Promise.all(deleteJobsQueries);
        await Promise.all(deletePreventiveMaintQueries);

        await Promise.all([
          dataSources.Machine.updateManyWithDeleted(
            { _id: { $in: allCustomerMachines.map(m => m._id) } },
            {
              $set: { customer: null, isQRCodeEnabled: false },
              $pull: { teams: { $in: oemCustomer?.teams } },
            },
          ),
        ]);
      } else {
        return new Error("you are not allowed to delete this customer");
      }

      return "ok";
    },

    assignMultipleCustomersToTeam: async (_, args, { dataSources, user }) => {
      const { input } = args || {};
      if (!input) return null;
      const { team, customers } = input;
      if (!team || !customers?.length) return null;
      const oemTeam = await dataSources.Team.getOne({
        $and: [{ _id: team }, belongsToUserTeams(user, "_id")],
        oem: user.organization,
      });

      if (!oemTeam) return throwIfError("Team not found");

      await dataSources.Customer.bulkWrite(
        customers?.map(customerId => ({
          updateOne: {
            filter: {
              _id: customerId,
              oem: user.organization,
              ...belongsToUserTeams(user),
            },
            update: { $addToSet: { teams: team } },
          },
        })),
      );

      const updatedCustomers = await dataSources.Customer.getMany({
        _id: { $in: customers },
        ...belongsToUserTeams(user),
        oem: user.organization,
      });

      return updatedCustomers;
    },
    unassignCustomerFromTeam: async (_, args, { dataSources, user }) => {
      const { input } = args || {};
      if (!input) return null;
      const { customer, team } = input;

      if (!customer || !team) return null;

      const unassignedCustomer = await dataSources.Customer.Customer.findOneAndUpdate(
        {
          _id: customer,
          $and: [{ teams: team }, belongsToUserTeams(user)],
          oem: user.organization,
        },
        { $pull: { teams: team } },
        { new: true },
      );

      return unassignedCustomer;
    },
  },
};
