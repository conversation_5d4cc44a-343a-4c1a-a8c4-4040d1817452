export default `#graphql
  input InputCreateOemCustomer {
    name: String!
  }

  input InputAssetAccess {
    documentation: Boolean
    parts: Boolean
    preventiveMaintenance: Boolean
    _3dModel: Boolean
    qrCodes: Boolean
    history: Boolean
  }

  input InputUpdateOemCustomer {
    _id: ID!
    name: String
    description: String
    customFields: [InputCustomField]
    facilityId: String
    qrCodeAccess: String
    isMachineDocumentationEnabled: Boolean
    isPreventiveMaintenanceEventsEnabled: Boolean
    assetAccess: InputAssetAccess
    type: String
  }

  input InputCreateOemCustomerV2 {
    name: String!
    machines: [ID!]
    users: [InputCreateFacilityUserV2!]
    teams: [String]
    facilityIdentifier: String
    type: String
  }

  input InputAssignMultipleCustomersToTeam {
    customers: [ID!]!
    team: ID!
  }

  input InputUnassignCustomerFromTeam {
    team: ID!
    customer: ID!
  }
`;
